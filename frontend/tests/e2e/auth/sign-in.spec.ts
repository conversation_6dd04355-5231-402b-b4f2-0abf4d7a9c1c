import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad, signInUser } from '../utils/helpers.js';
import { deleteTestUser } from '../utils/test-users.js';

// Test user for sign-in tests - created via UI sign-up
const SIGNIN_TEST_USER = {
	name: 'Sign In Test User',
	email: `signin-test-${Date.now()}@example.com`,
	password: 'SignInTest123!',
};

test.describe('Sign-in Page', () => {
	// Create test user via UI before running tests
	test.beforeAll(async ({ browser }) => {
		await deleteTestUser(SIGNIN_TEST_USER.email);

		// Create test user via sign-up form
		const page = await browser.newPage();
		try {
			await page.goto('/sign-up');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Name').fill(SIGNIN_TEST_USER.name);
			await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
			await page.getByPlaceholder('Password').fill(SIGNIN_TEST_USER.password);

			await page.getByRole('button', { name: 'Sign Up with Email' }).click();
			await page.waitForURL((url) => !url.pathname.includes('/sign-up'));
		} finally {
			await page.close();
		}
	});

	// Clean up test user after all tests complete
	test.afterAll(async () => {
		await deleteTestUser(SIGNIN_TEST_USER.email);
	});

	test('allows access to sign-in page when signed out', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
		await expect(page.getByPlaceholder('Email address')).toBeVisible();
		await expect(page.getByPlaceholder('Password')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
	});

	test('redirects away from sign-in page when already signed in', async ({ page, goto }) => {
		await signInUser(page, SIGNIN_TEST_USER.email, SIGNIN_TEST_USER.password);
		await goto('/sign-in');
		await waitForPageLoad(page);

		await expect(page).not.toHaveURL('/sign-in');
		await expect(page).toHaveURL('/');
	});

	test('successfully signs in existing user', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
		await page.getByPlaceholder('Password').fill(SIGNIN_TEST_USER.password);

		await page.getByRole('button', { name: 'Sign In' }).click();
		await page.waitForURL((url) => !url.pathname.includes('/sign-in'));

		await expect(page).toHaveURL('/');
		await expect(page.getByText(`Hello ${SIGNIN_TEST_USER.name}`)).toBeVisible();
	});

	test('shows error for invalid sign-in credentials', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Email address').fill('<EMAIL>');
		await page.getByPlaceholder('Password').fill('wrongpassword');

		await page.getByRole('button', { name: 'Sign In' }).click();

		await expect(page.getByText(/Invalid email or password|invalid credentials/i)).toBeVisible({ timeout: 10000 });
		await expect(page).toHaveURL('/sign-in');
	});

	test('navigates to sign-up page from sign-in page', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByText('Sign up for an account').click();

		await expect(page).toHaveURL('/sign-up');
		await expect(page.getByText('Sign up to Foundation')).toBeVisible();
	});
});
