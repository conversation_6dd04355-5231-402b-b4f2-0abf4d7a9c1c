import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad, signInUser } from '../helpers.js';
import { deleteTestUser, generateTestUserData } from '../test-users.js';
import { signUp } from '../../../server/utils/auth-client.js';

// Test user for sign-up tests - created programmatically via Better Auth API
const SIGNUP_TEST_USER = {
	name: 'Sign Up Test User',
	email: `signup-test-${Date.now()}@example.com`,
	password: 'SignUpTest123!',
};

test.describe('Sign-up Page', () => {
	// Create test user programmatically before running tests (for redirect and existing email tests)
	test.beforeAll(async () => {
		await deleteTestUser(SIGNUP_TEST_USER.email);

		try {
			const result = await signUp.email({
				name: SIGNUP_TEST_USER.name,
				email: SIGNUP_TEST_USER.email,
				password: SIGNUP_TEST_USER.password,
			});
			console.log('Test user creation result:', result);
		} catch (error) {
			console.log('Test user creation failed:', error);
			throw error;
		}
	});

	// Clean up test user after all tests complete
	test.afterAll(async () => {
		await deleteTestUser(SIGNUP_TEST_USER.email);
	});

	test('successfully signs up a new user with email/password', async ({ page, goto }) => {
		const testUserData = generateTestUserData({ name: 'New Test User' });

		try {
			await goto('/sign-up');
			await waitForPageLoad(page);

			await expect(page.getByText('Sign up to Foundation')).toBeVisible();

			await page.getByPlaceholder('Name').fill(testUserData.name);
			await page.getByPlaceholder('Email address').fill(testUserData.email);
			await page.getByPlaceholder('Password').fill(testUserData.password);

			await page.getByRole('button', { name: 'Sign Up with Email' }).click();
			await page.waitForURL((url) => !url.pathname.includes('/sign-up'));

			const currentUrl = page.url();
			expect(currentUrl.includes('/') || currentUrl.includes('/verify')).toBeTruthy();
		} finally {
			await deleteTestUser(testUserData.email);
		}
	});

	test('shows validation errors for invalid sign-up data', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Name').fill('A');
		await page.getByPlaceholder('Email address').fill('invalid-email');
		await page.getByPlaceholder('Password').fill('weak');

		await page.getByRole('button', { name: 'Sign Up with Email' }).click();
		await expect(page).toHaveURL('/sign-up');
	});

	test('shows error when trying to sign up with existing email', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Name').fill('Duplicate User');
		await page.getByPlaceholder('Email address').fill(SIGNUP_TEST_USER.email);
		await page.getByPlaceholder('Password').fill('ValidPass123!');

		await page.getByRole('button', { name: 'Sign Up with Email' }).click();
		await expect(page.getByText(/User already exists|already exists|duplicate/i)).toBeVisible({ timeout: 10000 });
		await expect(page).toHaveURL('/sign-up');
	});

	test('redirects away from sign-up page when already signed in', async ({ page, goto }) => {
		await signInUser(page, SIGNUP_TEST_USER.email, SIGNUP_TEST_USER.password);
		await goto('/sign-up');
		await waitForPageLoad(page);

		await expect(page).not.toHaveURL('/sign-up');
		await expect(page).toHaveURL('/');
	});

	test('navigates to sign-in page from sign-up page', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		await page.getByText('Already have an account?').click();

		await expect(page).toHaveURL('/sign-in');
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
	});
});
