import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad, signInUser } from './helpers.js';
import { deleteTestUser, generateTestUserData } from './test-users.js';
import { signUp } from '../../server/utils/auth-client.js';

// Test user for sign-in tests - created programmatically via Better Auth API
const SIGNIN_TEST_USER = {
	name: 'Sign In Test User',
	email: `signin-test-${Date.now()}@example.com`,
	password: 'SignInTest123!',
};

test.describe('Authentication Pages', () => {
	// Create test user programmatically before running tests
	test.beforeAll(async () => {
		await deleteTestUser(SIGNIN_TEST_USER.email);

		try {
			await signUp.email({
				name: SIGNIN_TEST_USER.name,
				email: SIGNIN_TEST_USER.email,
				password: SIGNIN_TEST_USER.password,
			});
		} catch (error) {
			console.log('Test user creation failed:', error);
			throw error;
		}
	});

	// Clean up test user after all tests complete
	test.afterAll(async () => {
		await deleteTestUser(SIGNIN_TEST_USER.email);
	});

	test.describe('Sign-in Page', () => {
		test('allows access to login page when signed out', async ({ page, goto }) => {
			await goto('/sign-in');
			await waitForPageLoad(page);

			await expect(page.getByText('Sign in to Foundation')).toBeVisible();
			await expect(page.getByPlaceholder('Email address')).toBeVisible();
			await expect(page.getByPlaceholder('Password')).toBeVisible();
			await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
		});

		test('redirects away from login page when already signed in', async ({ page, goto }) => {
			await signInUser(page, SIGNIN_TEST_USER.email, SIGNIN_TEST_USER.password);
			await goto('/sign-in');
			await waitForPageLoad(page);

			await expect(page).not.toHaveURL('/sign-in');
			await expect(page).toHaveURL('/');
		});

		test('successfully signs in existing user', async ({ page, goto }) => {
			await goto('/sign-in');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
			await page.getByPlaceholder('Password').fill(SIGNIN_TEST_USER.password);

			await page.getByRole('button', { name: 'Sign In' }).click();
			await page.waitForURL((url) => !url.pathname.includes('/sign-in'));

			await expect(page).toHaveURL('/');
			await expect(page.getByText(`Hello ${SIGNIN_TEST_USER.name}`)).toBeVisible();
		});

		test('shows error for invalid sign-in credentials', async ({ page, goto }) => {
			await goto('/sign-in');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Email address').fill('<EMAIL>');
			await page.getByPlaceholder('Password').fill('wrongpassword');

			await page.getByRole('button', { name: 'Sign In' }).click();

			await expect(page.getByText(/Invalid email or password|invalid credentials/i)).toBeVisible({ timeout: 10000 });
			await expect(page).toHaveURL('/sign-in');
		});
	});

	test.describe('Sign-up Page', () => {
		test('successfully signs up a new user with email/password', async ({ page, goto }) => {
			const testUserData = generateTestUserData({ name: 'New Test User' });

			try {
				await goto('/sign-up');
				await waitForPageLoad(page);

				await expect(page.getByText('Sign up to Foundation')).toBeVisible();

				await page.getByPlaceholder('Name').fill(testUserData.name);
				await page.getByPlaceholder('Email address').fill(testUserData.email);
				await page.getByPlaceholder('Password').fill(testUserData.password);

				await page.getByRole('button', { name: 'Sign Up with Email' }).click();
				await page.waitForURL((url) => !url.pathname.includes('/sign-up'));

				const currentUrl = page.url();
				expect(currentUrl.includes('/') || currentUrl.includes('/verify')).toBeTruthy();
			} finally {
				await deleteTestUser(testUserData.email);
			}
		});

		test('shows validation errors for invalid sign-up data', async ({ page, goto }) => {
			await goto('/sign-up');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Name').fill('A');
			await page.getByPlaceholder('Email address').fill('invalid-email');
			await page.getByPlaceholder('Password').fill('weak');

			await page.getByRole('button', { name: 'Sign Up with Email' }).click();
			await expect(page).toHaveURL('/sign-up');
		});

		test('shows error when trying to sign up with existing email', async ({ page, goto }) => {
			await goto('/sign-up');
			await waitForPageLoad(page);

			await page.getByPlaceholder('Name').fill('Duplicate User');
			await page.getByPlaceholder('Email address').fill(SIGNIN_TEST_USER.email);
			await page.getByPlaceholder('Password').fill('ValidPass123!');

			await page.getByRole('button', { name: 'Sign Up with Email' }).click();
			await expect(page.getByText(/User already exists|already exists|duplicate/i)).toBeVisible({ timeout: 10000 });
			await expect(page).toHaveURL('/sign-up');
		});
	});

	test.describe('Navigation Between Auth Pages', () => {
		test('navigates to sign-in page from sign-up page', async ({ page, goto }) => {
			await goto('/sign-up');
			await waitForPageLoad(page);

			await page.getByText('Already have an account?').click();

			await expect(page).toHaveURL('/sign-in');
			await expect(page.getByText('Sign in to Foundation')).toBeVisible();
		});

		test('navigates to sign-up page from sign-in page', async ({ page, goto }) => {
			await goto('/sign-in');
			await waitForPageLoad(page);

			await page.getByText('Sign up for an account').click();

			await expect(page).toHaveURL('/sign-up');
			await expect(page.getByText('Sign up to Foundation')).toBeVisible();
		});
	});
});
