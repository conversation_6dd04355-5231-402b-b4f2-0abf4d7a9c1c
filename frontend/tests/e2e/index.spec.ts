import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from './helpers.js';

test.describe('Authentication', () => {
	test('shows sign-in message when not authenticated', async ({ page, goto }) => {
		// Set a longer timeout for this test in CI
		test.slow();

		// Navigate to the home page
		await goto('/');

		// Wait for the page to be fully loaded
		await waitForPageLoad(page);

		// Check that user is logged out by verifying the sign-in link is visible
		await expect(page.getByText('Please')).toBeVisible();
		await expect(page.getByRole('link', { name: 'sign in' })).toBeVisible();
	});
});
