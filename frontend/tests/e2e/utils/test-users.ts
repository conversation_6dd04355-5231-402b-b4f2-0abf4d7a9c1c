/**
 * Test user management utilities
 *
 * This file provides utilities for managing test users in e2e tests.
 * Users are created programmatically via Better Auth API to ensure proper integration.
 */

import { drizzle } from 'drizzle-orm/neon-serverless';
import { neon } from '@neondatabase/serverless';
import { user } from '../../../server/db/schema/auth.js';
import { eq } from 'drizzle-orm';

// Initialize database connection for test user management
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

/**
 * Interface for test user data
 */
export interface TestUserData {
	name: string;
	email: string;
	password: string;
}

/**
 * Generates test user data with unique email
 * @param overrides Optional overrides for default values
 * @returns TestUserData object
 */
export function generateTestUserData(overrides: Partial<TestUserData> = {}): TestUserData {
	const timestamp = Date.now();
	return {
		name: 'Test User',
		email: `test-user-${timestamp}@example.com`,
		password: 'TestPassword123!',
		...overrides,
	};
}

/**
 * Deletes a test user from the database
 * @param email Email of the user to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	try {
		await db.delete(user).where(eq(user.email, email));
		console.log(`Deleted test user: ${email}`);
	} catch (error) {
		console.log(`Failed to delete test user ${email}:`, error);
		// Don't throw here as the user might not exist
	}
}
