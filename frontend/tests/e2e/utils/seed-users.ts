/**
 * Database seeding utilities for E2E tests
 *
 * This file provides utilities for seeding test users directly into the database
 * using the same password hashing approach as Better Auth (scrypt).
 */

import { drizzle } from 'drizzle-orm/neon-serverless';
import { neon } from '@neondatabase/serverless';
import { user, account } from '../../../server/db/schema/auth.js';
import { eq } from 'drizzle-orm';
import { scrypt, randomBytes } from 'crypto';
import { promisify } from 'util';

// Promisify scrypt for async/await usage
const scryptAsync = promisify(scrypt);

// Initialize database connection for test user seeding
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

/**
 * Interface for test user data
 */
export interface TestUserData {
	name: string;
	email: string;
	password: string;
}

/**
 * Generates test user data with unique email
 * @param overrides Optional overrides for default values
 * @returns TestUserData object
 */
export function generateTestUserData(overrides: Partial<TestUserData> = {}): TestUserData {
	const timestamp = Date.now();
	return {
		name: 'Test User',
		email: `test-user-${timestamp}@example.com`,
		password: 'TestPassword123!',
		...overrides,
	};
}

/**
 * Hashes a password using scrypt (same algorithm as Better Auth)
 * @param password Plain text password
 * @returns Hashed password string
 */
async function hashPassword(password: string): Promise<string> {
	// Generate a random salt (16 bytes)
	const salt = randomBytes(16);
	
	// Hash the password using scrypt with the same parameters as Better Auth
	// Better Auth typically uses: N=16384, r=8, p=1, keylen=64
	const derivedKey = (await scryptAsync(password, salt, 64)) as Buffer;
	
	// Combine salt and derived key, then encode as base64
	// Format: salt (16 bytes) + derivedKey (64 bytes) = 80 bytes total
	const combined = Buffer.concat([salt, derivedKey]);
	return combined.toString('base64');
}

/**
 * Generates a unique user ID (similar to Better Auth's approach)
 * @returns Unique user ID string
 */
function generateUserId(): string {
	// Generate a random ID similar to Better Auth's format
	return randomBytes(16).toString('base64url');
}

/**
 * Seeds a test user directly into the database
 * @param userData User data to seed
 * @returns Created user object
 */
export async function seedTestUser(userData: TestUserData) {
	const hashedPassword = await hashPassword(userData.password);
	const userId = generateUserId();
	const now = new Date();

	try {
		// Insert user into the user table
		const [createdUser] = await db.insert(user).values({
			id: userId,
			name: userData.name,
			email: userData.email,
			emailVerified: false, // Match your auth config
			image: null,
			createdAt: now,
			updatedAt: now,
		}).returning();

		// Insert account record for email/password authentication
		await db.insert(account).values({
			id: generateUserId(),
			accountId: userId, // Link to the user
			providerId: 'credential', // Better Auth uses 'credential' for email/password
			userId: userId,
			accessToken: null,
			refreshToken: null,
			idToken: null,
			accessTokenExpiresAt: null,
			refreshTokenExpiresAt: null,
			scope: null,
			password: hashedPassword, // Store the hashed password
			createdAt: now,
			updatedAt: now,
		});

		return createdUser;
	} catch (error) {
		console.error('Failed to seed test user:', error);
		throw error;
	}
}

/**
 * Deletes a test user from the database (including related account records)
 * @param email Email of the user to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	try {
		// Find the user first
		const [userToDelete] = await db.select().from(user).where(eq(user.email, email));
		
		if (userToDelete) {
			// Delete related account records first (foreign key constraint)
			await db.delete(account).where(eq(account.userId, userToDelete.id));
			
			// Then delete the user
			await db.delete(user).where(eq(user.email, email));
		}
	} catch (error) {
		// Don't throw here as the user might not exist
	}
}
