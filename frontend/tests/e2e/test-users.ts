/**
 * Test user management utilities
 *
 * This file provides utilities for managing test users in e2e tests.
 * Users are created programmatically via Better Auth API to ensure proper integration.
 */
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq } from 'drizzle-orm';
import { user } from '../../server/db/schema/auth.js';
import { loadTestEnv } from '../../server/utils/env-loader.js';

// Load test environment
loadTestEnv();

// Create database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

export interface TestUser {
	id: string;
	name: string;
	email: string;
	password: string;
}

/**
 * Deletes a test user from the database
 * @param email User email to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	// Find user by email
	const userRecord = await db.select().from(user).where(eq(user.email, email)).limit(1);

	if (userRecord.length > 0) {
		// Delete user (CASCADE will automatically delete related records)
		await db.delete(user).where(eq(user.id, userRecord[0].id));
	}
}

/**
 * Generates a unique test email
 * @param prefix Email prefix
 * @returns Unique test email
 */
export function generateTestEmail(prefix = 'test'): string {
	const timestamp = Date.now();
	const random = Math.random().toString(36).substring(7);
	return `${prefix}-${timestamp}-${random}@example.com`;
}

/**
 * Generates test user data
 * @param overrides Optional overrides for user data
 * @returns Test user data
 */
export function generateTestUserData(overrides: Partial<Omit<TestUser, 'id'>> = {}): Omit<TestUser, 'id'> {
	return {
		name: 'Test User',
		email: generateTestEmail(),
		password: 'TestPass123!',
		...overrides,
	};
}
